#!/usr/bin/env python3
"""
Test script to verify all dependencies are installed correctly
"""

print("Testing crypto TA API dependencies...")

try:
    import fastapi
    print("✅ FastAPI imported successfully")
except ImportError as e:
    print(f"❌ FastAPI import failed: {e}")

try:
    import uvicorn
    print("✅ Uvicorn imported successfully")
except ImportError as e:
    print(f"❌ Uvicorn import failed: {e}")

try:
    import ccxt
    print("✅ CCXT imported successfully")
except ImportError as e:
    print(f"❌ CCXT import failed: {e}")

try:
    import pandas as pd
    print("✅ Pandas imported successfully")
except ImportError as e:
    print(f"❌ Pandas import failed: {e}")

try:
    import numpy as np
    # Workaround for pandas-ta compatibility
    if not hasattr(np, 'NaN'):
        np.NaN = np.nan
    print("✅ Numpy imported successfully (with compatibility fix)")
except ImportError as e:
    print(f"❌ Numpy import failed: {e}")

try:
    import pandas_ta as ta
    print("✅ Pandas-TA imported successfully")
except ImportError as e:
    print(f"❌ Pandas-TA import failed: {e}")

print("\n" + "="*50)
print("Testing basic functionality...")

try:
    # Test CCXT exchange connection
    exchange = ccxt.binance()
    print("✅ Binance exchange initialized")
    
    # Test basic pandas operations
    df = pd.DataFrame({'test': [1, 2, 3]})
    print("✅ Pandas DataFrame created")
    
    # Test FastAPI app creation
    app = fastapi.FastAPI()
    print("✅ FastAPI app created")
    
    print("\n🎉 All tests passed! Your crypto TA API is ready to run.")
    print("\nTo start the server, run:")
    print("venv\\Scripts\\python.exe -m uvicorn ta_api:app --reload")
    
except Exception as e:
    print(f"❌ Functionality test failed: {e}")
