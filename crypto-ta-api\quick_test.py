#!/usr/bin/env python3
"""
Quick test to verify the server can start
"""
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔍 Testing server startup...")
    
    # Import our API
    from ta_api import app
    print("✅ API imported successfully")
    
    # Try to start uvicorn
    import uvicorn
    print("✅ Uvicorn imported successfully")
    
    print("🚀 Starting server on http://127.0.0.1:8000")
    print("📝 Test URL: http://127.0.0.1:8000/get-ta-data?symbol=BTC/USDT")
    print("🛑 Press Ctrl+C to stop")
    print("-" * 50)
    
    # Start the server
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
    
except Exception as e:
    print(f"❌ Error starting server: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
