@echo off
echo 🚀 Starting Crypto Technical Analysis API v2.0...
echo 📊 Available endpoints:
echo   - GET /get-ta-data?symbol=BTC/USDT^&timeframe=1h^&limit=200
echo   - GET /get-order-book?symbol=BTC/USDT^&limit=10
echo   - GET /get-klines?symbol=BTC/USDT^&timeframe=1h^&limit=100
echo   - GET /get-mta-data?symbol=BTC/USDT
echo   - GET /docs (API documentation)
echo 🌐 Server will be available at:
echo   - Local: http://localhost:8000
echo   - Network: http://[YOUR_IP]:8000
echo ⚠️  Make sure Windows Firewall allows port 8000
echo.
echo Press Ctrl+C to stop the server
echo.

venv\Scripts\python.exe ta_api.py

pause
