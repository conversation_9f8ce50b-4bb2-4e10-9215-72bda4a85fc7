#!/usr/bin/env python3
"""
Test script to check which US exchanges work and what symbols are available
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from exchange_config import get_exchange, US_EXCHANGES

def test_exchange(exchange_name):
    """Test if an exchange is accessible and get some basic info"""
    print(f"\n🔍 Testing {exchange_name}...")
    
    try:
        exchange = get_exchange(exchange_name)
        
        # Test basic connectivity
        markets = exchange.load_markets()
        print(f"✅ {exchange_name} connected successfully!")
        print(f"📊 Available markets: {len(markets)}")
        
        # Find common trading pairs
        common_pairs = ['BTC/USD', 'BTC/USDT', 'ETH/USD', 'ETH/USDT']
        available_pairs = []
        
        for pair in common_pairs:
            if pair in markets:
                available_pairs.append(pair)
        
        print(f"💰 Common pairs available: {available_pairs}")
        
        # Test fetching some data
        if available_pairs:
            test_pair = available_pairs[0]
            try:
                ticker = exchange.fetch_ticker(test_pair)
                print(f"📈 {test_pair} price: ${ticker['last']:.2f}")
                return True, available_pairs
            except Exception as e:
                print(f"⚠️  Could not fetch ticker for {test_pair}: {e}")
                return True, available_pairs
        else:
            print("⚠️  No common trading pairs found")
            return True, []
            
    except Exception as e:
        print(f"❌ {exchange_name} failed: {e}")
        return False, []

def main():
    print("🇺🇸 Testing US-Friendly Crypto Exchanges")
    print("=" * 50)
    
    working_exchanges = {}
    
    for exchange_name in US_EXCHANGES.keys():
        success, pairs = test_exchange(exchange_name)
        if success:
            working_exchanges[exchange_name] = pairs
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    
    if working_exchanges:
        print("✅ Working exchanges:")
        for exchange_name, pairs in working_exchanges.items():
            exchange_info = US_EXCHANGES[exchange_name]
            print(f"  • {exchange_info['name']} ({exchange_name})")
            print(f"    Available pairs: {pairs}")
        
        print(f"\n💡 Recommendation: Use '{list(working_exchanges.keys())[0]}' as your primary exchange")
        print(f"   To change exchange, edit CURRENT_EXCHANGE in ta_api.py")
    else:
        print("❌ No exchanges are currently accessible")
        print("   This might be due to network issues or API restrictions")

if __name__ == "__main__":
    main()
