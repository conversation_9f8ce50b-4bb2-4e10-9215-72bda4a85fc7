#!/usr/bin/env python3
"""
Check what exchanges are available in CCXT
"""

import ccxt

print("🔍 Checking available exchanges in CCXT...")
print("=" * 50)

# List all exchanges
all_exchanges = ccxt.exchanges
print(f"📊 Total exchanges available: {len(all_exchanges)}")

# Check for US-friendly exchanges
us_friendly = []
exchange_names_to_check = [
    'binance', 'binanceus', 'coinbase', 'coinbasepro', 'coinbaseadvanced',
    'kraken', 'kucoin', 'gemini', 'bittrex', 'bitstamp'
]

print("\n🇺🇸 Checking US-friendly exchanges:")
for name in exchange_names_to_check:
    if name in all_exchanges:
        print(f"✅ {name} - Available")
        us_friendly.append(name)
    else:
        print(f"❌ {name} - Not found")

print(f"\n📋 Available US-friendly exchanges: {us_friendly}")

# Test creating exchange instances
print("\n🧪 Testing exchange creation:")
for exchange_name in us_friendly[:4]:  # Test first 4
    try:
        exchange_class = getattr(ccxt, exchange_name)
        exchange = exchange_class({'enableRateLimit': True})
        print(f"✅ {exchange_name} - Can create instance")
    except Exception as e:
        print(f"❌ {exchange_name} - Error: {e}")
