"""
Exchange configuration for US-friendly crypto exchanges
"""
import ccxt

# Available US-friendly exchanges
US_EXCHANGES = {
    'binanceus': {
        'name': 'Binance US',
        'description': 'US version of Binance - most similar to Binance.com',
        'pros': ['Familiar API', 'Good liquidity', 'Many trading pairs'],
        'cons': ['Fewer pairs than Binance.com'],
        'popular_pairs': ['BTC/USD', 'ETH/USD', 'ADA/USD', 'DOT/USD']
    },
    'coinbase': {
        'name': 'Coinbase',
        'description': 'Large US exchange with high liquidity',
        'pros': ['Very reliable', 'High liquidity', 'Regulatory compliant'],
        'cons': ['Higher fees', 'Fewer altcoins'],
        'popular_pairs': ['BTC/USD', 'ETH/USD', 'LTC/USD', 'BCH/USD']
    },
    'kraken': {
        'name': 'Kraken',
        'description': 'Well-established exchange, US-friendly',
        'pros': ['Good API', 'Many trading pairs', 'Reliable'],
        'cons': ['Different interface'],
        'popular_pairs': ['BTC/USD', 'ETH/USD', 'XRP/USD', 'ADA/USD']
    },
    'kucoin': {
        'name': 'KuCoin',
        'description': 'Global exchange available to US users',
        'pros': ['Many trading pairs', 'Good API', 'Lower fees'],
        'cons': ['Less regulated'],
        'popular_pairs': ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT']
    },
    'gemini': {
        'name': 'Gemini',
        'description': 'US-based exchange founded by Winklevoss twins',
        'pros': ['Highly regulated', 'Good security', 'US-based'],
        'cons': ['Fewer trading pairs', 'Higher fees'],
        'popular_pairs': ['BTC/USD', 'ETH/USD', 'LTC/USD', 'BCH/USD']
    }
}

def get_exchange(exchange_name='binanceus'):
    """
    Get a configured exchange instance

    Args:
        exchange_name (str): Name of the exchange ('binanceus', 'coinbase', 'kraken', 'kucoin', 'gemini')

    Returns:
        ccxt.Exchange: Configured exchange instance
    """
    exchange_configs = {
        'binanceus': ccxt.binanceus({
            'enableRateLimit': True,
            'sandbox': False,
        }),
        'coinbase': ccxt.coinbase({
            'enableRateLimit': True,
            'sandbox': False,
        }),
        'kraken': ccxt.kraken({
            'enableRateLimit': True,
        }),
        'kucoin': ccxt.kucoin({
            'enableRateLimit': True,
            'sandbox': False,
        }),
        'gemini': ccxt.gemini({
            'enableRateLimit': True,
            'sandbox': False,
        })
    }
    
    if exchange_name not in exchange_configs:
        raise ValueError(f"Exchange '{exchange_name}' not supported. Available: {list(exchange_configs.keys())}")
    
    return exchange_configs[exchange_name]

def list_exchanges():
    """Print information about available exchanges"""
    print("🇺🇸 Available US-Friendly Exchanges:")
    print("=" * 50)
    
    for exchange_id, info in US_EXCHANGES.items():
        print(f"\n📊 {info['name']} ({exchange_id})")
        print(f"   {info['description']}")
        print(f"   ✅ Pros: {', '.join(info['pros'])}")
        print(f"   ⚠️  Cons: {', '.join(info['cons'])}")
        print(f"   💰 Popular pairs: {', '.join(info['popular_pairs'])}")

if __name__ == "__main__":
    list_exchanges()
