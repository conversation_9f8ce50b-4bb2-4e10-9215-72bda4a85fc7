# Install required libraries first:
# pip install fastapi uvicorn ccxt pandas pandas-ta

import uvicorn
from fastapi import FastAPI
import ccxt
import pandas as pd
import pandas_ta as ta

# Initialize the FastAPI app
app = FastAPI()

# Initialize the exchange from CCXT (e.g., Binance)
exchange = ccxt.binance()

@app.get("/get-ta-data")
def get_technical_analysis(symbol: str = 'BTC/USDT', timeframe: str = '1h', limit: int = 200):
    """
    This endpoint fetches market data, calculates technical indicators,
    and returns them as a JSON object.
    """
    try:
        # 1. Fetch OHLCV (Open, High, Low, Close, Volume) data
        bars = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
        df = pd.DataFrame(bars, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

        # 2. Calculate Technical Indicators using pandas-ta
        # You can add as many as you want here!
        df.ta.rsi(length=14, append=True)
        df.ta.bbands(length=20, append=True) # Adds 5 columns for Bollinger Bands
        df.ta.macd(append=True)
        
        # 3. Get the most recent indicator values
        latest_indicators = df.iloc[-1]

        # 4. Prepare and return the data as JSON
        # Note: We convert numpy types to native Python types for JSON compatibility
        return {
            "symbol": symbol,
            "timestamp": latest_indicators['timestamp'].isoformat(),
            "close": float(latest_indicators['close']),
            "rsi": float(latest_indicators['RSI_14']),
            "bollinger_upper": float(latest_indicators['BBU_20_2.0']),
            "bollinger_lower": float(latest_indicators['BBL_20_2.0']),
            "macd": float(latest_indicators['MACD_12_26_9']),
            "macdsignal": float(latest_indicators['MACDs_12_26_9']),
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/")
def read_root():
    """
    Root endpoint that provides information about the API
    """
    return {
        "message": "Crypto Technical Analysis API",
        "version": "1.0.0",
        "endpoints": {
            "/get-ta-data": "Get technical analysis data for a crypto symbol",
            "/docs": "Interactive API documentation"
        },
        "example_usage": "/get-ta-data?symbol=BTC/USDT&timeframe=1h&limit=200"
    }

@app.get("/health")
def health_check():
    """
    Health check endpoint
    """
    return {"status": "healthy", "service": "crypto-ta-api"}

# To run this script:
# 1. Save it as ta_api.py
# 2. Open your terminal in the same directory
# 3. Run the command: uvicorn ta_api:app --reload
# 4. It will now be running at http://127.0.0.1:8000

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)
