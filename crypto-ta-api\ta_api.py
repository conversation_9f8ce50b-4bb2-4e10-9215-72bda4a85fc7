# Install required libraries first:
# pip install fastapi uvicorn python-ccxt pandas pandas-ta

import uvicorn
from fastapi import FastAPI
import ccxt
import pandas as pd
import numpy as np # Import numpy for data type conversion

# Workaround for pandas-ta compatibility with newer numpy
import sys
if not hasattr(np, 'NaN'):
    np.NaN = np.nan

import pandas_ta as ta
from typing import List
from exchange_config import get_exchange, US_EXCHANGES

# Initialize the FastAPI app
app = FastAPI()

# Initialize the exchange from CCXT (US-friendly option)
# You can change the exchange here: 'binanceus', 'coinbasepro', 'kraken', 'kucoin'
CURRENT_EXCHANGE = 'binanceus'  # Change this to switch exchanges
exchange = get_exchange(CURRENT_EXCHANGE)

@app.get("/get-ta-data")
def get_technical_analysis(symbol: str = 'BTC/USDT', timeframe: str = '1h', limit: int = 200):
    """
    This endpoint fetches market data, calculates a wide range of technical indicators,
    including VWAP, and returns them as a JSON object.
    """
    try:
        # 1. Fetch OHLCV (Open, High, Low, Close, Volume) data
        bars = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
        df = pd.DataFrame(bars, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

        # 2. Calculate a richer set of Technical Indicators using pandas-ta
        df.ta.rsi(length=14, append=True)
        df.ta.bbands(length=20, append=True)
        df.ta.macd(append=True)
        df.ta.obv(append=True)
        df.ta.atr(length=14, append=True)
        df.ta.stoch(k=14, d=3, append=True)
        df.ta.vwap(append=True) # --- NEW: Added VWAP calculation
        
        # 3. Get the most recent indicator values
        latest_indicators = df.iloc[-1]

        # 4. Prepare and return the data as JSON, ensuring numpy types are converted
        return {
            "symbol": symbol,
            "timestamp": latest_indicators['timestamp'].isoformat(),
            "close": float(latest_indicators['close']),
            "rsi": float(latest_indicators['RSI_14']),
            "bollinger_upper": float(latest_indicators['BBU_20_2.0']),
            "bollinger_lower": float(latest_indicators['BBL_20_2.0']),
            "macd": float(latest_indicators['MACD_12_26_9']),
            "macdsignal": float(latest_indicators['MACDs_12_26_9']),
            "on_balance_volume": float(latest_indicators['OBV']),
            "average_true_range": float(latest_indicators['ATRr_14']),
            "stoch_k": float(latest_indicators['STOCHk_14_3_3']),
            "stoch_d": float(latest_indicators['STOCHd_14_3_3']),
            "vwap": float(latest_indicators['VWAP_D']) # --- NEW: Added VWAP to response
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/get-order-book")
def get_order_book(symbol: str = 'BTC/USDT', limit: int = 10):
    """
    This endpoint fetches the current order book (bids and asks)
    and returns a summary.
    """
    try:
        order_book = exchange.fetch_order_book(symbol, limit=limit)
        bids = order_book['bids']
        asks = order_book['asks']
        top_bids_volume = sum([amount for price, amount in bids])
        top_asks_volume = sum([amount for price, amount in asks])
        
        return {
            "symbol": symbol,
            "highest_bid": bids[0][0] if bids else 0,
            "lowest_ask": asks[0][0] if asks else 0,
            "bid_ask_spread": (asks[0][0] - bids[0][0]) if (bids and asks) else 0,
            "top_bids_volume": top_bids_volume,
            "top_asks_volume": top_asks_volume,
            "imbalance": top_bids_volume / (top_bids_volume + top_asks_volume) if (top_bids_volume + top_asks_volume) > 0 else 0.5
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/get-klines")
def get_klines(symbol: str = 'BTC/USDT', timeframe: str = '1h', limit: int = 100):
    """
    This endpoint fetches raw OHLCV (kline) data for a given symbol and timeframe.
    """
    try:
        bars = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
        return bars
    except Exception as e:
        return {"error": str(e)}

# --- NEW ENDPOINT: MULTI-TIMEFRAME ANALYSIS ---
@app.get("/get-mta-data")
def get_mta_data(symbol: str = 'BTC/USDT'):
    """
    This endpoint provides key indicators (RSI, MACD) across multiple timeframes
    (15m, 1h, 4h) to give a comprehensive view of the trend.
    """
    timeframes = ['15m', '1h', '4h']
    mta_results = {}

    try:
        for tf in timeframes:
            # Fetch data for the timeframe
            bars = exchange.fetch_ohlcv(symbol, timeframe=tf, limit=100)
            df = pd.DataFrame(bars, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Calculate indicators
            df.ta.rsi(length=14, append=True)
            df.ta.macd(append=True)
            
            # Get the latest values
            latest_indicators = df.iloc[-1]
            
            # Store the results
            mta_results[tf] = {
                "rsi": float(latest_indicators['RSI_14']),
                "macd": float(latest_indicators['MACD_12_26_9']),
                "macd_signal": float(latest_indicators['MACDs_12_26_9']),
                "is_bullish_cross": float(latest_indicators['MACD_12_26_9']) > float(latest_indicators['MACDs_12_26_9'])
            }
        
        return mta_results

    except Exception as e:
        return {"error": str(e)}

@app.get("/exchanges")
def get_available_exchanges():
    """
    Get information about available US-friendly exchanges
    """
    return {
        "current_exchange": CURRENT_EXCHANGE,
        "available_exchanges": US_EXCHANGES,
        "note": "To change exchange, modify CURRENT_EXCHANGE variable in ta_api.py"
    }

@app.get("/")
def read_root():
    """
    Root endpoint that provides information about the API
    """
    return {
        "message": "Crypto Technical Analysis API",
        "version": "2.0.0",
        "current_exchange": CURRENT_EXCHANGE,
        "exchange_info": US_EXCHANGES.get(CURRENT_EXCHANGE, {}),
        "endpoints": {
            "/get-ta-data": "Get technical analysis data for a crypto symbol",
            "/get-order-book": "Get order book data (bids/asks)",
            "/get-klines": "Get raw OHLCV data",
            "/get-mta-data": "Get multi-timeframe analysis",
            "/exchanges": "List available US-friendly exchanges",
            "/docs": "Interactive API documentation"
        },
        "example_usage": {
            "ta_data": "/get-ta-data?symbol=BTC/USDT&timeframe=1h&limit=200",
            "order_book": "/get-order-book?symbol=BTC/USDT&limit=10",
            "klines": "/get-klines?symbol=BTC/USDT&timeframe=1h&limit=100",
            "mta": "/get-mta-data?symbol=BTC/USDT"
        }
    }

@app.get("/health")
def health_check():
    """
    Health check endpoint
    """
    return {"status": "healthy", "service": "crypto-ta-api", "version": "2.0.0"}

# To run this script:
# 1. Save it as ta_api.py
# 2. Open your terminal in the same directory and activate your virtual environment
# 3. Run the command: uvicorn ta_api:app --reload
# 4. The server will run on http://127.0.0.1:8000

if __name__ == "__main__":
    print("🚀 Starting Crypto Technical Analysis API v2.0...")
    print("📊 Available endpoints:")
    print("  - GET /get-ta-data?symbol=BTC/USDT&timeframe=1h&limit=200")
    print("  - GET /get-order-book?symbol=BTC/USDT&limit=10")
    print("  - GET /get-klines?symbol=BTC/USDT&timeframe=1h&limit=100")
    print("  - GET /get-mta-data?symbol=BTC/USDT")
    print("  - GET /docs (API documentation)")
    print("🌐 Server will be available at:")
    print("  - Local: http://localhost:8000")
    print("  - Network: http://[YOUR_IP]:8000")
    print("⚠️  Make sure Windows Firewall allows port 8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
